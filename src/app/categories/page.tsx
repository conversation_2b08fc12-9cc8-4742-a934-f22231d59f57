"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/components/layout";
import { ClientImage } from "@/components/ui/client-image";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowRight,
  Package,
  Search,
  Grid,
  List,
  Filter,
  TrendingUp,
  Star,
  Eye,
  ShoppingBag,
  Layers,
  ChevronRight,
  Heart,
  ShoppingCart,
} from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";

interface Category {
  id: string;
  name: string;
  description?: string;
  image?: string;
  slug: string;
  parentId?: string;
  _count: {
    products: number;
    children: number;
  };
  children: Category[];
  products?: Array<{
    id: string;
    name: string;
    price: number;
    salePrice?: number;
    images: string[];
    slug: string;
  }>;
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("name");
  const [activeTab, setActiveTab] = useState("all");
  const [wishlist, setWishlist] = useState<string[]>([]);

  // Mock data for demonstration when no categories exist
  const mockCategories: Category[] = [
    {
      id: "1",
      name: "Thời trang nữ",
      description: "Bộ sưu tập thời trang nữ đa dạng và phong cách",
      slug: "thoi-trang-nu",
      _count: { products: 156, children: 8 },
      children: [
        {
          id: "1-1",
          name: "Áo",
          slug: "ao-nu",
          _count: { products: 45, children: 0 },
          children: [],
        },
        {
          id: "1-2",
          name: "Quần",
          slug: "quan-nu",
          _count: { products: 38, children: 0 },
          children: [],
        },
        {
          id: "1-3",
          name: "Váy",
          slug: "vay",
          _count: { products: 32, children: 0 },
          children: [],
        },
      ],
      products: [
        {
          id: "p1",
          name: "Áo sơ mi trắng",
          price: 299000,
          slug: "ao-so-mi-trang",
          images: ["/images/products/shirt-1.jpg"],
        },
        {
          id: "p2",
          name: "Quần jeans skinny",
          price: 599000,
          salePrice: 449000,
          slug: "quan-jeans-skinny",
          images: ["/images/products/jeans-1.jpg"],
        },
      ],
    },
    {
      id: "2",
      name: "Thời trang nam",
      description: "Phong cách thời trang nam hiện đại và lịch lãm",
      slug: "thoi-trang-nam",
      _count: { products: 134, children: 6 },
      children: [
        {
          id: "2-1",
          name: "Áo",
          slug: "ao-nam",
          _count: { products: 52, children: 0 },
          children: [],
        },
        {
          id: "2-2",
          name: "Quần",
          slug: "quan-nam",
          _count: { products: 41, children: 0 },
          children: [],
        },
      ],
      products: [
        {
          id: "p3",
          name: "Áo polo nam",
          price: 399000,
          slug: "ao-polo-nam",
          images: ["/images/products/polo-1.jpg"],
        },
        {
          id: "p4",
          name: "Quần kaki",
          price: 499000,
          slug: "quan-kaki",
          images: ["/images/products/kaki-1.jpg"],
        },
      ],
    },
    {
      id: "3",
      name: "Phụ kiện",
      description: "Phụ kiện thời trang hoàn thiện phong cách của bạn",
      slug: "phu-kien",
      _count: { products: 89, children: 4 },
      children: [
        {
          id: "3-1",
          name: "Túi xách",
          slug: "tui-xach",
          _count: { products: 28, children: 0 },
          children: [],
        },
        {
          id: "3-2",
          name: "Giày dép",
          slug: "giay-dep",
          _count: { products: 35, children: 0 },
          children: [],
        },
      ],
      products: [
        {
          id: "p5",
          name: "Túi xách da",
          price: 899000,
          salePrice: 699000,
          slug: "tui-xach-da",
          images: ["/images/products/bag-1.jpg"],
        },
        {
          id: "p6",
          name: "Giày sneaker",
          price: 1299000,
          slug: "giay-sneaker",
          images: ["/images/products/sneaker-1.jpg"],
        },
      ],
    },
  ];

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch("/api/categories?includeProducts=true");
        const data = await response.json();

        if (response.ok) {
          // Filter only root categories (no parent)
          const rootCategories = data.filter((cat: Category) => !cat.parentId);
          if (rootCategories.length > 0) {
            setCategories(rootCategories);
          } else {
            // Use mock data when no categories exist
            setCategories(mockCategories);
          }
        } else {
          // Use mock data on error
          setCategories(mockCategories);
        }
      } catch (error) {
        // Use mock data on error
        setCategories(mockCategories);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [mockCategories]);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 container mx-auto px-4 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-8" />
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }, (_, i) => (
                <Card key={i}>
                  <div className="aspect-video bg-gray-200 rounded-t-lg" />
                  <CardContent className="p-4 space-y-2">
                    <div className="h-4 bg-gray-200 rounded" />
                    <div className="h-4 bg-gray-200 rounded w-2/3" />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        {/* Enhanced Page Header */}
        <div className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Layers className="h-6 w-6 text-fashion-500" />
            <h1 className="text-3xl lg:text-4xl font-bold">
              Danh mục sản phẩm
            </h1>
          </div>
          <p className="text-lg text-muted-foreground mb-6">
            Khám phá các danh mục thời trang đa dạng với hơn{" "}
            {categories.reduce((total, cat) => total + cat._count.products, 0)}{" "}
            sản phẩm
          </p>

          {/* Search and Filters */}
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm danh mục..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-fashion-500 focus:border-transparent text-sm"
              >
                <option value="name">Tên A-Z</option>
                <option value="products">Nhiều sản phẩm nhất</option>
                <option value="popular">Phổ biến nhất</option>
              </select>

              <div className="flex items-center gap-1 border rounded-lg p-1">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="h-8 w-8 p-0"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="h-8 w-8 p-0"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Category Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList className="grid w-full grid-cols-4 lg:w-auto lg:grid-cols-none lg:flex">
            <TabsTrigger value="all">Tất cả</TabsTrigger>
            <TabsTrigger value="women">Nữ</TabsTrigger>
            <TabsTrigger value="men">Nam</TabsTrigger>
            <TabsTrigger value="accessories">Phụ kiện</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-6">
            {/* All Categories Content */}
          </TabsContent>
          <TabsContent value="women" className="mt-6">
            {/* Women Categories Content */}
          </TabsContent>
          <TabsContent value="men" className="mt-6">
            {/* Men Categories Content */}
          </TabsContent>
          <TabsContent value="accessories" className="mt-6">
            {/* Accessories Categories Content */}
          </TabsContent>
        </Tabs>

        {/* Enhanced Categories Grid */}
        <div
          className={`grid gap-6 ${
            viewMode === "grid"
              ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3"
              : "grid-cols-1"
          }`}
        >
          {categories
            .filter(
              (category) =>
                searchTerm === "" ||
                category.name
                  .toLowerCase()
                  .includes(searchTerm.toLowerCase()) ||
                category.description
                  ?.toLowerCase()
                  .includes(searchTerm.toLowerCase())
            )
            .sort((a, b) => {
              switch (sortBy) {
                case "products":
                  return b._count.products - a._count.products;
                case "popular":
                  return b._count.products - a._count.products; // Using product count as popularity metric
                default:
                  return a.name.localeCompare(b.name);
              }
            })
            .map((category) => (
              <Card
                key={category.id}
                className={`group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm ${
                  viewMode === "list" ? "flex flex-row" : ""
                }`}
              >
                <Link
                  href={`/categories/${category.slug}`}
                  className={viewMode === "list" ? "flex flex-1" : ""}
                >
                  <div
                    className={`relative overflow-hidden bg-gradient-to-br from-fashion-100 to-fashion-200 ${
                      viewMode === "list"
                        ? "w-48 h-32"
                        : "aspect-video rounded-t-xl"
                    }`}
                  >
                    {category.image ? (
                      <ClientImage
                        src={category.image}
                        alt={category.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                        fallbackSrc="/images/placeholder.jpg"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <Package className="h-16 w-16 text-fashion-400" />
                      </div>
                    )}
                    <div className="absolute inset-0 bg-black/20 group-hover:bg-black/30 transition-colors" />

                    {/* Category Stats Overlay */}
                    <div className="absolute top-4 right-4 flex flex-col gap-1">
                      <Badge className="bg-white/90 text-fashion-600 text-xs">
                        {category._count.products} sản phẩm
                      </Badge>
                      {category._count.children > 0 && (
                        <Badge
                          variant="secondary"
                          className="bg-white/90 text-xs"
                        >
                          {category._count.children} danh mục con
                        </Badge>
                      )}
                    </div>

                    {/* Category Name Overlay */}
                    <div className="absolute bottom-4 left-4 text-white">
                      <h3
                        className={`font-bold mb-1 ${
                          viewMode === "list" ? "text-lg" : "text-xl"
                        }`}
                      >
                        {category.name}
                      </h3>
                      {viewMode === "grid" && (
                        <p className="text-sm opacity-90">
                          {category._count.products} sản phẩm
                        </p>
                      )}
                    </div>
                  </div>
                </Link>

                <CardContent
                  className={`${viewMode === "list" ? "flex-1 p-6" : "p-4"}`}
                >
                  {viewMode === "list" && (
                    <div className="flex items-center justify-between mb-3">
                      <Link href={`/categories/${category.slug}`}>
                        <h3 className="text-xl font-bold hover:text-fashion-600 transition-colors">
                          {category.name}
                        </h3>
                      </Link>
                      <Link
                        href={`/categories/${category.slug}`}
                        className="text-fashion-600 hover:text-fashion-700 transition-colors"
                      >
                        <ArrowRight className="h-5 w-5" />
                      </Link>
                    </div>
                  )}

                  {category.description && (
                    <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                      {category.description}
                    </p>
                  )}

                  {/* Enhanced Subcategories */}
                  {category.children.length > 0 && (
                    <div className="mb-4">
                      <div className="flex items-center gap-2 mb-2">
                        <Layers className="h-3 w-3 text-muted-foreground" />
                        <p className="text-xs font-medium text-muted-foreground">
                          Danh mục con:
                        </p>
                      </div>
                      <div className="flex flex-wrap gap-1">
                        {category.children.slice(0, 3).map((child) => (
                          <Link
                            key={child.id}
                            href={`/categories/${child.slug}`}
                            className="group"
                          >
                            <Badge
                              variant="outline"
                              className="text-xs hover:bg-fashion-50 hover:border-fashion-200 transition-colors"
                            >
                              {child.name}
                              <ChevronRight className="h-3 w-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                            </Badge>
                          </Link>
                        ))}
                        {category.children.length > 3 && (
                          <Badge variant="secondary" className="text-xs">
                            +{category.children.length - 3} khác
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Enhanced Featured Products */}
                  {category.products && category.products.length > 0 && (
                    <div className="mb-4">
                      <div className="flex items-center gap-2 mb-3">
                        <Star className="h-3 w-3 text-muted-foreground" />
                        <p className="text-xs font-medium text-muted-foreground">
                          Sản phẩm nổi bật:
                        </p>
                      </div>
                      <div
                        className={`grid gap-2 ${
                          viewMode === "list" ? "grid-cols-6" : "grid-cols-4"
                        }`}
                      >
                        {category.products
                          .slice(0, viewMode === "list" ? 6 : 4)
                          .map((product) => (
                            <div key={product.id} className="group relative">
                              <Link
                                href={`/products/${product.slug}`}
                                className="relative aspect-square overflow-hidden rounded-lg bg-gradient-to-br from-muted to-muted-foreground/10 hover:shadow-md transition-all duration-200 block"
                              >
                                <div className="flex items-center justify-center h-full">
                                  <div className="text-lg font-bold text-muted-foreground/30">
                                    {product.name.charAt(0)}
                                  </div>
                                </div>
                                {product.salePrice && (
                                  <div className="absolute top-1 right-1 bg-red-500 text-white text-xs px-1 rounded">
                                    Sale
                                  </div>
                                )}
                              </Link>
                              <div className="mt-1">
                                <p className="text-xs font-medium line-clamp-1">
                                  {product.name}
                                </p>
                                <p className="text-xs text-fashion-600 font-semibold">
                                  {product.salePrice
                                    ? formatCurrency(product.salePrice)
                                    : formatCurrency(product.price)}
                                </p>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}

                  {/* Enhanced Stats */}
                  <div className="flex items-center justify-between pt-3 border-t border-muted">
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <ShoppingBag className="h-3 w-3" />
                        <span>{category._count.products} sản phẩm</span>
                      </div>
                      {category._count.children > 0 && (
                        <div className="flex items-center gap-1">
                          <Layers className="h-3 w-3" />
                          <span>{category._count.children} danh mục con</span>
                        </div>
                      )}
                    </div>
                    <Link
                      href={`/categories/${category.slug}`}
                      className="text-fashion-600 hover:text-fashion-700 transition-colors"
                    >
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
        </div>

        {/* Popular Categories Section */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold mb-6">Danh mục phổ biến</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {categories
              .sort((a, b) => b._count.products - a._count.products)
              .slice(0, 6)
              .map((category) => (
                <Link
                  key={category.id}
                  href={`/categories/${category.slug}`}
                  className="group"
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4 text-center">
                      <div className="relative w-16 h-16 mx-auto mb-3 overflow-hidden rounded-full bg-gradient-to-br from-pink-100 to-purple-100">
                        {category.image ? (
                          <ClientImage
                            src={category.image}
                            alt={category.name}
                            fill
                            className="object-cover group-hover:scale-110 transition-transform duration-300"
                            fallbackSrc="/images/placeholder.jpg"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full">
                            <Package className="h-8 w-8 text-pink-400" />
                          </div>
                        )}
                      </div>
                      <h3 className="font-medium text-sm mb-1 group-hover:text-pink-600 transition-colors">
                        {category.name}
                      </h3>
                      <p className="text-xs text-muted-foreground">
                        {category._count.products} sản phẩm
                      </p>
                    </CardContent>
                  </Card>
                </Link>
              ))}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
