import { useState, useEffect } from 'react';
import { Category } from '@/types';

interface UseCategoriesOptions {
  includeProducts?: boolean;
  parentId?: string | null;
}

interface UseCategoriesReturn {
  categories: Category[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export function useCategories(options: UseCategoriesOptions = {}): UseCategoriesReturn {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      
      if (options.includeProducts) params.append('includeProducts', 'true');
      if (options.parentId !== undefined) {
        if (options.parentId === null) {
          params.append('parentId', 'null');
        } else {
          params.append('parentId', options.parentId);
        }
      }

      const response = await fetch(`/api/categories?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }

      const data = await response.json();
      setCategories(data.categories || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, [options.includeProducts, options.parentId]);

  return {
    categories,
    loading,
    error,
    refetch: fetchCategories,
  };
}

// Hook chuyên dụng cho root categories (không có parent)
export function useRootCategories() {
  return useCategories({ parentId: null });
}
