import { useState, useEffect } from 'react';
import { Product } from '@/types';

interface UseProductsOptions {
  featured?: boolean;
  category?: string;
  search?: string;
  limit?: number;
  page?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

interface UseProductsReturn {
  products: Product[];
  loading: boolean;
  error: string | null;
  total: number;
  refetch: () => void;
}

export function useProducts(options: UseProductsOptions = {}): UseProductsReturn {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      
      if (options.featured) params.append('featured', 'true');
      if (options.category) params.append('category', options.category);
      if (options.search) params.append('search', options.search);
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.page) params.append('page', options.page.toString());
      if (options.sortBy) params.append('sortBy', options.sortBy);
      if (options.sortOrder) params.append('sortOrder', options.sortOrder);

      const response = await fetch(`/api/products?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }

      const data = await response.json();
      setProducts(data.products || []);
      setTotal(data.total || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setProducts([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [
    options.featured,
    options.category,
    options.search,
    options.limit,
    options.page,
    options.sortBy,
    options.sortOrder,
  ]);

  return {
    products,
    loading,
    error,
    total,
    refetch: fetchProducts,
  };
}

// Hook chuyên dụng cho featured products
export function useFeaturedProducts(limit: number = 6) {
  return useProducts({ featured: true, limit });
}

// Hook chuyên dụng cho trending products (tạm thời dùng featured)
export function useTrendingProducts(limit: number = 4) {
  return useProducts({ featured: true, limit, sortBy: 'createdAt', sortOrder: 'desc' });
}
