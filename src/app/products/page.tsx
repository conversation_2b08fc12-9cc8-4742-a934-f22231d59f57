"use client";

import { useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON>, <PERSON>er } from "@/components/layout";
import { ClientImage } from "@/components/ui/client-image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import {
  Search,
  Filter,
  Grid,
  List,
  Star,
  Heart,
  ShoppingCart,
  Eye,
  GitCompare,
  X,
  SlidersHorizontal,
  TrendingUp,
  Clock,
  Zap,
} from "lucide-react";
import { toast } from "@/lib/toast";
import { formatCurrency } from "@/lib/utils";

interface Product {
  id: string;
  name: string;
  price: number;
  salePrice?: number;
  images: string[];
  slug: string;
  avgRating: number;
  reviewCount: number;
  category: {
    id: string;
    name: string;
    slug: string;
  };
  brand?: string;
  tags?: string[];
  isNew?: boolean;
  isTrending?: boolean;
  isBestseller?: boolean;
  stockQuantity?: number;
  attributes?: {
    color?: string[];
    size?: string[];
    material?: string;
  };
}

interface Category {
  id: string;
  name: string;
  slug: string;
  productCount?: number;
}

interface Brand {
  id: string;
  name: string;
  productCount: number;
}

interface AdvancedFilters {
  priceRange: [number, number];
  brands: string[];
  colors: string[];
  sizes: string[];
  tags: string[];
  rating: number;
  inStock: boolean;
}

function ProductsPageContent() {
  const searchParams = useSearchParams();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);
  const [compareList, setCompareList] = useState<string[]>([]);
  const [wishlist, setWishlist] = useState<string[]>([]);
  const [recentlyViewed, setRecentlyViewed] = useState<Product[]>([]);

  const [filters, setFilters] = useState({
    search: searchParams.get("search") || "",
    category: searchParams.get("category") || "",
    sortBy: searchParams.get("sortBy") || "createdAt",
    sortOrder: searchParams.get("sortOrder") || "desc",
  });

  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFilters>({
    priceRange: [0, 5000000],
    brands: [],
    colors: [],
    sizes: [],
    tags: [],
    rating: 0,
    inStock: false,
  });

  const [pagination, setPagination] = useState({
    page: 1,
    limit: 12,
    total: 0,
    pages: 0,
  });

  // Fetch products
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.search && { search: filters.search }),
        ...(filters.category && { category: filters.category }),
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
      });

      const response = await fetch(`/api/products?${params}`);
      const data = await response.json();

      if (response.ok) {
        setProducts(data.products);
        setPagination(data.pagination);
      } else {
        toast.error("Có lỗi xảy ra khi tải sản phẩm");
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi tải sản phẩm");
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/categories");
      const data = await response.json();

      if (response.ok) {
        setCategories(data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchProducts();
  }, [filters, pagination.page]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination({ ...pagination, page: 1 });
    fetchProducts();
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters({ ...filters, [key]: value });
    setPagination({ ...pagination, page: 1 });
  };

  const handleAdvancedFilterChange = (
    key: keyof AdvancedFilters,
    value: any
  ) => {
    setAdvancedFilters({ ...advancedFilters, [key]: value });
    setPagination({ ...pagination, page: 1 });
  };

  const toggleWishlist = (productId: string) => {
    setWishlist((prev) =>
      prev.includes(productId)
        ? prev.filter((id) => id !== productId)
        : [...prev, productId]
    );
  };

  const toggleCompare = (productId: string) => {
    if (compareList.includes(productId)) {
      setCompareList((prev) => prev.filter((id) => id !== productId));
    } else if (compareList.length < 3) {
      setCompareList((prev) => [...prev, productId]);
    } else {
      toast.error("Chỉ có thể so sánh tối đa 3 sản phẩm");
    }
  };

  const addToRecentlyViewed = (product: Product) => {
    setRecentlyViewed((prev) => {
      const filtered = prev.filter((p) => p.id !== product.id);
      return [product, ...filtered].slice(0, 5);
    });
  };

  const clearFilters = () => {
    setAdvancedFilters({
      priceRange: [0, 5000000],
      brands: [],
      colors: [],
      sizes: [],
      tags: [],
      rating: 0,
      inStock: false,
    });
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${
          i < Math.floor(rating)
            ? "text-yellow-400 fill-current"
            : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />

      <main className="flex-1 container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Sản phẩm</h1>
          <p className="text-muted-foreground">
            Khám phá bộ sưu tập thời trang mới nhất
          </p>
        </div>

        {/* Enhanced Search and Filters */}
        <div className="mb-8 space-y-6">
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Tìm kiếm sản phẩm, thương hiệu, danh mục..."
                value={filters.search}
                onChange={(e) =>
                  setFilters({ ...filters, search: e.target.value })
                }
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-fashion-500 focus:border-transparent text-sm"
              />
            </div>
            <Button type="submit" variant="fashion" size="lg">
              <Search className="h-4 w-4 mr-2" />
              Tìm kiếm
            </Button>
          </form>

          {/* Quick Filters */}
          <div className="flex flex-wrap gap-2">
            <Badge
              variant="outline"
              className="cursor-pointer hover:bg-fashion-50"
            >
              <TrendingUp className="h-3 w-3 mr-1" />
              Trending
            </Badge>
            <Badge
              variant="outline"
              className="cursor-pointer hover:bg-fashion-50"
            >
              <Zap className="h-3 w-3 mr-1" />
              Bestseller
            </Badge>
            <Badge
              variant="outline"
              className="cursor-pointer hover:bg-fashion-50"
            >
              <Clock className="h-3 w-3 mr-1" />
              Mới nhất
            </Badge>
            <Badge
              variant="outline"
              className="cursor-pointer hover:bg-fashion-50"
            >
              Sale
            </Badge>
          </div>

          {/* Main Filters and Controls */}
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex flex-wrap items-center gap-4">
              {/* Advanced Filters Toggle */}
              <Sheet open={showFilters} onOpenChange={setShowFilters}>
                <SheetTrigger asChild>
                  <Button variant="outline" size="sm">
                    <SlidersHorizontal className="h-4 w-4 mr-2" />
                    Bộ lọc nâng cao
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-80">
                  <SheetHeader>
                    <SheetTitle>Bộ lọc nâng cao</SheetTitle>
                  </SheetHeader>
                  <div className="mt-6 space-y-6">
                    {/* Price Range */}
                    <div>
                      <h4 className="font-medium mb-3">Khoảng giá</h4>
                      <Slider
                        value={advancedFilters.priceRange}
                        onValueChange={(value) =>
                          handleAdvancedFilterChange("priceRange", value)
                        }
                        max={5000000}
                        step={100000}
                        className="mb-2"
                      />
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>
                          {formatCurrency(advancedFilters.priceRange[0])}
                        </span>
                        <span>
                          {formatCurrency(advancedFilters.priceRange[1])}
                        </span>
                      </div>
                    </div>

                    <Separator />

                    {/* Rating Filter */}
                    <div>
                      <h4 className="font-medium mb-3">Đánh giá tối thiểu</h4>
                      <div className="space-y-2">
                        {[5, 4, 3, 2, 1].map((rating) => (
                          <div
                            key={rating}
                            className="flex items-center space-x-2"
                          >
                            <Checkbox
                              checked={advancedFilters.rating === rating}
                              onCheckedChange={() =>
                                handleAdvancedFilterChange("rating", rating)
                              }
                            />
                            <div className="flex items-center">
                              {Array.from({ length: rating }, (_, i) => (
                                <Star
                                  key={i}
                                  className="h-3 w-3 text-yellow-400 fill-current"
                                />
                              ))}
                              <span className="ml-1 text-sm">trở lên</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Stock Filter */}
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={advancedFilters.inStock}
                        onCheckedChange={(checked) =>
                          handleAdvancedFilterChange("inStock", checked)
                        }
                      />
                      <label className="text-sm">
                        Chỉ hiển thị sản phẩm còn hàng
                      </label>
                    </div>

                    {/* Clear Filters */}
                    <Button
                      variant="outline"
                      onClick={clearFilters}
                      className="w-full"
                    >
                      Xóa tất cả bộ lọc
                    </Button>
                  </div>
                </SheetContent>
              </Sheet>

              {/* Category Filter */}
              <select
                value={filters.category}
                onChange={(e) => handleFilterChange("category", e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-fashion-500 focus:border-transparent text-sm"
              >
                <option value="">Tất cả danh mục</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}{" "}
                    {category.productCount && `(${category.productCount})`}
                  </option>
                ))}
              </select>

              {/* Sort Filter */}
              <select
                value={`${filters.sortBy}-${filters.sortOrder}`}
                onChange={(e) => {
                  const [sortBy, sortOrder] = e.target.value.split("-");
                  setFilters({ ...filters, sortBy, sortOrder });
                }}
                className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-fashion-500 focus:border-transparent text-sm"
              >
                <option value="createdAt-desc">Mới nhất</option>
                <option value="createdAt-asc">Cũ nhất</option>
                <option value="price-asc">Giá thấp đến cao</option>
                <option value="price-desc">Giá cao đến thấp</option>
                <option value="name-asc">Tên A-Z</option>
                <option value="name-desc">Tên Z-A</option>
                <option value="rating-desc">Đánh giá cao nhất</option>
                <option value="popularity-desc">Phổ biến nhất</option>
              </select>
            </div>

            {/* View Mode and Compare */}
            <div className="flex items-center gap-2">
              {compareList.length > 0 && (
                <Button variant="outline" size="sm">
                  <GitCompare className="h-4 w-4 mr-2" />
                  So sánh ({compareList.length})
                </Button>
              )}

              <div className="flex items-center gap-1 border rounded-lg p-1">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="h-8 w-8 p-0"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="h-8 w-8 p-0"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Active Filters Display */}
          {(advancedFilters.brands.length > 0 ||
            advancedFilters.rating > 0 ||
            advancedFilters.inStock) && (
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Bộ lọc đang áp dụng:
              </span>
              {advancedFilters.rating > 0 && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  {advancedFilters.rating}+ sao
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleAdvancedFilterChange("rating", 0)}
                  />
                </Badge>
              )}
              {advancedFilters.inStock && (
                <Badge variant="secondary" className="flex items-center gap-1">
                  Còn hàng
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => handleAdvancedFilterChange("inStock", false)}
                  />
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Products Grid/List */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }, (_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="aspect-square bg-gray-200 rounded-t-lg" />
                <CardContent className="p-4 space-y-2">
                  <div className="h-4 bg-gray-200 rounded" />
                  <div className="h-4 bg-gray-200 rounded w-2/3" />
                  <div className="h-4 bg-gray-200 rounded w-1/2" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-muted-foreground">Không tìm thấy sản phẩm nào</p>
          </div>
        ) : (
          <div
            className={
              viewMode === "grid"
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
                : "space-y-4"
            }
          >
            {products.map((product) => (
              <Card
                key={product.id}
                className={`group hover:shadow-lg transition-shadow ${
                  viewMode === "list" ? "flex" : ""
                }`}
              >
                <Link href={`/products/${product.slug}`} className="block">
                  <div
                    className={`relative overflow-hidden ${
                      viewMode === "list"
                        ? "w-48 h-48 flex-shrink-0"
                        : "aspect-square"
                    } rounded-t-lg`}
                  >
                    <ClientImage
                      src={product.images[0]}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                      fallbackSrc="/images/placeholder.jpg"
                    />
                    {product.salePrice && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                        -
                        {Math.round(
                          ((product.price - product.salePrice) /
                            product.price) *
                            100
                        )}
                        %
                      </div>
                    )}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="h-8 w-8 p-0"
                      >
                        <Heart className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Link>

                <CardContent
                  className={`p-4 ${viewMode === "list" ? "flex-1" : ""}`}
                >
                  <Link href={`/products/${product.slug}`}>
                    <h3 className="font-medium text-sm mb-2 line-clamp-2 hover:text-pink-600 transition-colors">
                      {product.name}
                    </h3>
                  </Link>

                  <div className="flex items-center gap-1 mb-2">
                    {renderStars(product.avgRating)}
                    <span className="text-xs text-muted-foreground ml-1">
                      ({product.reviewCount})
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      {product.salePrice ? (
                        <>
                          <div className="text-lg font-bold text-pink-600">
                            {formatCurrency(product.salePrice)}
                          </div>
                          <div className="text-sm text-muted-foreground line-through">
                            {formatCurrency(product.price)}
                          </div>
                        </>
                      ) : (
                        <div className="text-lg font-bold">
                          {formatCurrency(product.price)}
                        </div>
                      )}
                    </div>

                    <Button
                      size="sm"
                      className="bg-pink-600 hover:bg-pink-700 h-8 w-8 p-0"
                      onClick={(e) => {
                        e.preventDefault();
                        // Add to cart logic here
                        toast.success("Đã thêm vào giỏ hàng");
                      }}
                    >
                      <ShoppingCart className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="text-xs text-muted-foreground mt-2">
                    {product.category.name}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.pages > 1 && (
          <div className="flex justify-center items-center gap-2 mt-8">
            <Button
              variant="outline"
              disabled={pagination.page === 1}
              onClick={() =>
                setPagination({ ...pagination, page: pagination.page - 1 })
              }
            >
              Trước
            </Button>

            {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
              const page = i + 1;
              return (
                <Button
                  key={page}
                  variant={pagination.page === page ? "default" : "outline"}
                  onClick={() => setPagination({ ...pagination, page })}
                >
                  {page}
                </Button>
              );
            })}

            <Button
              variant="outline"
              disabled={pagination.page === pagination.pages}
              onClick={() =>
                setPagination({ ...pagination, page: pagination.page + 1 })
              }
            >
              Sau
            </Button>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}

export default function ProductsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ProductsPageContent />
    </Suspense>
  );
}
